version: '3.8'

services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # You must change these. Replace `ai.guiyunai.fun` with your DNS name - this needs to be exactly the URL you're accessing Postiz on.
      MAIN_URL: "https://ai.guiyunai.fun"
      FRONTEND_URL: "https://ai.guiyunai.fun"
      NEXT_PUBLIC_BACKEND_URL: "https://ai.guiyunai.fun/api"
      JWT_SECRET: "yunguizhongguo2024postiz-production-jwt-secret-key-very-long-and-secure-random-string"
 
      # These defaults are probably fine, but if you change your user/password, update it in the
      # postiz-postgres or postiz-redis services below.
      DATABASE_URL: "*************************************************************/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      IS_GENERAL: "true" # Required for self-hosting.
      DISABLE_REGISTRATION: "false" # Only allow single registration, then disable signup
      
      # The container images are pre-configured to use /uploads for file storage.
      # You probably should not change this unless you have a really good reason!
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
      # AI Configuration
      GROQ_API_KEY: "********************************************************"
      OPENAI_API_KEY: "********************************************************************************************************************************************************************"
      DEEPSEEK_API_KEY: "***********************************"
      
      # API Limits
      API_LIMIT: "30"
      
      # Developer Settings
      NX_ADD_PLUGINS: "false"
      
      # Branding
      NEXT_PUBLIC_POSTIZ_OAUTH_DISPLAY_NAME: "云归.中国"

      # === Social Media API Settings ===
      # X (Twitter) Configuration
      X_API_KEY: "*************************"
      X_API_SECRET: "A5MInTeREXLttl2zAeGDBYNCfVP8D6sone5JPTSd3YbGcbJZK2"

      # LinkedIn Configuration
      LINKEDIN_CLIENT_ID: ""
      LINKEDIN_CLIENT_SECRET: ""

      # YouTube Configuration (Google OAuth)
      YOUTUBE_CLIENT_ID: ""
      YOUTUBE_CLIENT_SECRET: ""

      # TikTok Configuration
      TIKTOK_CLIENT_ID: ""
      TIKTOK_CLIENT_SECRET: ""

      # Instagram/Facebook Configuration
      FACEBOOK_APP_ID: ""
      FACEBOOK_APP_SECRET: ""
      INSTAGRAM_APP_ID: ""
      INSTAGRAM_APP_SECRET: ""

      # Reddit Configuration
      REDDIT_CLIENT_ID: ""
      REDDIT_CLIENT_SECRET: ""

      # Discord Configuration
      DISCORD_CLIENT_ID: ""
      DISCORD_CLIENT_SECRET: ""
      DISCORD_BOT_TOKEN_ID: ""

      # Slack Configuration
      SLACK_ID: ""
      SLACK_SECRET: ""
      SLACK_SIGNING_SECRET: ""

      # Pinterest Configuration
      PINTEREST_CLIENT_ID: ""
      PINTEREST_CLIENT_SECRET: ""

      # Dribbble Configuration
      DRIBBBLE_CLIENT_ID: ""
      DRIBBBLE_CLIENT_SECRET: ""

      # Threads Configuration
      THREADS_APP_ID: ""
      THREADS_APP_SECRET: ""

      # Mastodon Configuration
      MASTODON_URL: "https://mastodon.social"
      MASTODON_CLIENT_ID: ""
      MASTODON_CLIENT_SECRET: ""
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - 5000:5000
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy

  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postiz-password
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3
      
  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network

volumes:
  postgres-volume:
    external: false

  postiz-redis-data:
    external: false

  postiz-config:
    external: false

  postiz-uploads:
    external: false

networks:
  postiz-network:
    external: false
